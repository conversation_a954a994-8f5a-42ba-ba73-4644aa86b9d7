﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;
using DevExpress.XtraBars;
using System.IO;
using HIH.Framework.Common.Excel;

namespace HIH.CRM.Import
{
    public partial class frmImportSTWK : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        #region 字段和属性
        private string currentFilePath = "";              // 当前Excel文件路径
        private DataTable dt = new DataTable();
        #endregion

        #region 构造函数和初始化

        public frmImportSTWK()
        {
            InitializeComponent();
            isLoadPerm = false;
        }


        #endregion

        #region 窗体事件

        private void frmImportSTWK_Load(object sender, EventArgs e)
        {
            try
            {
                // 添加列
                dt.Columns.Add("订单号", typeof(string));
                dt.Columns.Add("行号", typeof(string));
                dt.Columns.Add("供应商代码", typeof(string));
                dt.Columns.Add("供应商名称", typeof(string));
                dt.Columns.Add("料号", typeof(string));
                dt.Columns.Add("料号名称", typeof(string));
                dt.Columns.Add("订单数量", typeof(decimal));
                dt.Columns.Add("剩余数量", typeof(decimal));
                dt.Columns.Add("单位", typeof(string));
                dt.Columns.Add("状态", typeof(string));
                dt.Columns.Add("提货时间", typeof(DateTime));
                dt.Columns.Add("预计提货时间", typeof(DateTime));
                dt.Columns.Add("运输时间", typeof(int));
                dt.Columns.Add("处理人", typeof(string));
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"窗体加载失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion


        #region 工具栏按钮事件

        private void btnImportExcel_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                string sFileName = "";
                Boolean _b = ICF.ISD.OpenDialog("请选择需要导入的文件", "*.xlsx|*.xlsx|*.xls|*.xls", out sFileName);

                if (_b)
                {
                    if (string.IsNullOrEmpty(sFileName))
                    {
                        throw new Exception("请选择需要导入的文件！");
                    }

                    try
                    {
                        DataTable DT = NPOIHelper.ImportExceltoDt(sFileName, 1, 0);
                        for (int i = 0; i <= DT.Rows.Count - 1; i++)
                        {
                            if (DT.Rows[i][0].ToString() == "" || DT.Rows[i][1].ToString() == "")
                            {
                                break;
                            }
                            DataRow DR = dt.NewRow();
                            DR["处理人"] = DT.Rows[i][0].ToString();
                            DR["订单号"] = DT.Rows[i][1].ToString();
                            DR["行号"] = DT.Rows[i][2].ToString();
                            DR["供应商代码"] = DT.Rows[i][3].ToString();
                            DR["供应商名称"] = DT.Rows[i][4].ToString();
                            DR["料号"] = DT.Rows[i][5].ToString();
                            DR["料号名称"] = DT.Rows[i][6].ToString();
                            DR["订单数量"] = DT.Rows[i][7].ToString();
                            DR["剩余数量"] = DT.Rows[i][8].ToString();
                            DR["单位"] = DT.Rows[i][9].ToString();
                            DR["状态"] = DT.Rows[i][10].ToString();
                            DR["提货时间"] = DT.Rows[i][11].ToString();
                            DR["预计提货时间"] = DT.Rows[i][12].ToString();
                            DR["运输时间"] = DT.Rows[i][13].ToString();
                            dt.Rows.Add(DR);
                        }
                        gd.DataSource = dt;

                        ICF.ISD.ShowTips("导入成功！");

                    }
                    catch (Exception exc)
                    {
                        ICF.ISD.ShowError(exc.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"导入Excel失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private void btnSave_ItemClick(object sender, ItemClickEventArgs e)
        {

        }


        private void btnReturn_ItemClick(object sender, ItemClickEventArgs e)
        {
            DoReturn();
        }

        #endregion

        #region Excel导入处理

        private void ImportExcelFile(string filePath)
        {
            try
            {

            }
            catch (Exception ex)
            {
                throw new Exception($"读取Excel文件失败：{ex.Message}");
            }
        }

        #endregion

    }
}
