﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HIH.CRM.Import
{
    public partial class frmSTWKEdit : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        public frmSTWKEdit()
        {
            InitializeComponent();
            isLoadPerm = false;
        }

        #region 节点控制
        private void CHKDDJS_CheckedChanged(object sender, EventArgs e)
        {
            //ddJSSJ.ReadOnly = !chkDDJS.Checked;
            if (chkDDJS.Checked)
            {
                ddJSSJ.EditValue = DateTime.Now.ToString("yyyy-MM-dd");
                lblDDJSCZR.Text = ICF.ISO.URNAME;
                lblDDJSSJ.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                ddJSSJ.EditValue = DBNull.Value;
                lblDDJSCZR.Text = "";
                lblDDJSSJ.Text = "";
            }
        }

        private void chkFSDD_CheckedChanged(object sender, EventArgs e)
        {
            ddFSDD.ReadOnly = !chkFSDD.Checked;
            if (chkFSDD.Checked)
            {
                ddFSDD.EditValue = DateTime.Now.ToString("yyyy-MM-dd");
                lblFSDDCZR.Text = ICF.ISO.URNAME;
                lblFSDDSJ.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                ddFSDD.EditValue = DBNull.Value;
                lblFSDDCZR.Text = "";
                lblFSDDSJ.Text = "";
            }
        }

        private void chkFHRHF_CheckedChanged(object sender, EventArgs e)
        {
            ddFHRHF.ReadOnly = !chkFHRHF.Checked;
            if (chkFHRHF.Checked)
            {
                ddFHRHF.EditValue = DateTime.Now.ToString("yyyy-MM-dd");
                lblFHHFCZR.Text = ICF.ISO.URNAME;
                lblFHHFSJ.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                ddFHRHF.EditValue = DBNull.Value;
                lblFHHFCZR.Text = "";
                lblFHHFSJ.Text = "";
            }
        }

        private void chkSJJF_CheckedChanged(object sender, EventArgs e)
        {
            ddSJJF.ReadOnly = !chkSJJF.Checked;
            if (chkSJJF.Checked)
            {
                ddSJJF.EditValue = DateTime.Now.ToString("yyyy-MM-dd");
                lblSJJFCZR.Text = ICF.ISO.URNAME;
                lblSHHFSJ.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                ddSJJF.EditValue = DBNull.Value;
                lblSJJFCZR.Text = "";
                lblSHHFSJ.Text = "";
            }
        }
        #endregion


        private void frmSTWKEdit_Load(object sender, EventArgs e)
        {
            //接收数据的呢，并且赋值显示的呢
            if (vInParam == null) return;

            try
            {
                // 基本信息赋值
                txtOrder.Text = ((ArrayList)vInParam)[2]?.ToString() ?? "";        // 订单号
                txtRow.Text = ((ArrayList)vInParam)[3]?.ToString() ?? "";          // 行号
                txtSupplier.Text = ((ArrayList)vInParam)[4]?.ToString() ?? "";     // 供应商代码
                txtSupplName.Text = ((ArrayList)vInParam)[5]?.ToString() ?? "";    // 供应商名称
                txtItemId.Text = ((ArrayList)vInParam)[6]?.ToString() ?? "";       // 料号
                txtItemName.Text = ((ArrayList)vInParam)[7]?.ToString() ?? "";     // 料号名称
                txtQtyOrd.Text = ((ArrayList)vInParam)[8]?.ToString() ?? "";       // 订单数量
                txtLeftQty.Text = ((ArrayList)vInParam)[9]?.ToString() ?? "";      // 剩余数量
                txtUnit.Text = ((ArrayList)vInParam)[10]?.ToString() ?? "";        // 单位
                txtStatus.Text = ((ArrayList)vInParam)[11]?.ToString() ?? "";      // 状态
                txtDelDate.Text = ((ArrayList)vInParam)[12]?.ToString() ?? "";  //提货时间
                txtExtDate.Text = ((ArrayList)vInParam)[13]?.ToString() ?? "";  //延后交期
                txtPTT.Text = ((ArrayList)vInParam)[14]?.ToString() ?? ""; //
                ddJSSJ.EditValue = ((ArrayList)vInParam)[15]?.ToString() ?? ""; //
                txtYHJQ.EditValue = ((ArrayList)vInParam)[16]?.ToString() ?? "";


                if (!string.IsNullOrEmpty(ddJSSJ.Text))
                {
                    chkDDJS.Checked = true;
                    lblDDJSCZR.Text = "王志文";
                    lblDDJSSJ.Text = "2025-06-09";
                }
                else
                {
                    lblDDJSCZR.Text = "";
                    lblDDJSSJ.Text = "";
                }
               
                lblFSDDCZR.Text = "";
                lblFSDDSJ.Text = "";
                lblFHHFCZR.Text = "";
                lblFHHFSJ.Text = "";
                lblSJJFCZR.Text = "";
                lblSHHFSJ.Text = "";
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError("加载数据失败：" + ex.Message);
            }
        }

        private void btnExit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DoReturn();
        }

    }
}
