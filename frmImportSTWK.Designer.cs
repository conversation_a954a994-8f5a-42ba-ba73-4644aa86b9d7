﻿
namespace HIH.CRM.Import
{
    partial class frmImportSTWK
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmImportSTWK));
            this.groupControlImported = new DevExpress.XtraEditors.GroupControl();
            this.gd = new DevExpress.XtraGrid.GridControl();
            this.gdv = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.处理人 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.订单号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.行号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.供应商代码 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.供应商名称 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.料号 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.料号名称 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.订单数量 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.剩余数量 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.单位 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.状态 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.提货时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.预计提货时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.运输时间 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.bar3 = new DevExpress.XtraBars.Bar();
            this.barMenu = new DevExpress.XtraBars.BarManager(this.components);
            this.bar4 = new DevExpress.XtraBars.Bar();
            this.btnImportExcel = new DevExpress.XtraBars.BarButtonItem();
            this.btnSave = new DevExpress.XtraBars.BarButtonItem();
            this.btnExit = new DevExpress.XtraBars.BarButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.btnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.错误信息 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.bs)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlImported)).BeginInit();
            this.groupControlImported.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gd)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdv)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barMenu)).BeginInit();
            this.SuspendLayout();
            // 
            // groupControlImported
            // 
            this.groupControlImported.Controls.Add(this.gd);
            this.groupControlImported.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupControlImported.Location = new System.Drawing.Point(0, 24);
            this.groupControlImported.Name = "groupControlImported";
            this.groupControlImported.Size = new System.Drawing.Size(1176, 554);
            this.groupControlImported.TabIndex = 14;
            this.groupControlImported.Text = "Excel导入列表";
            // 
            // gd
            // 
            this.gd.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gd.Location = new System.Drawing.Point(2, 23);
            this.gd.MainView = this.gdv;
            this.gd.Name = "gd";
            this.gd.Size = new System.Drawing.Size(1172, 529);
            this.gd.TabIndex = 0;
            this.gd.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gdv});
            // 
            // gdv
            // 
            this.gdv.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.处理人,
            this.订单号,
            this.行号,
            this.供应商代码,
            this.供应商名称,
            this.料号,
            this.料号名称,
            this.订单数量,
            this.剩余数量,
            this.单位,
            this.状态,
            this.提货时间,
            this.预计提货时间,
            this.运输时间,
            this.错误信息});
            this.gdv.GridControl = this.gd;
            this.gdv.Name = "gdv";
            this.gdv.OptionsSelection.MultiSelect = true;
            this.gdv.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect;
            this.gdv.OptionsView.ColumnAutoWidth = false;
            this.gdv.OptionsView.EnableAppearanceEvenRow = true;
            this.gdv.OptionsView.ShowGroupPanel = false;
            this.gdv.OptionsView.ShowIndicator = false;
            // 
            // 处理人
            // 
            this.处理人.Caption = "处理人";
            this.处理人.FieldName = "处理人";
            this.处理人.Name = "处理人";
            this.处理人.Visible = true;
            this.处理人.VisibleIndex = 15;
            // 
            // 订单号
            // 
            this.订单号.Caption = "订单号";
            this.订单号.FieldName = "订单号";
            this.订单号.Name = "订单号";
            this.订单号.Visible = true;
            this.订单号.VisibleIndex = 1;
            // 
            // 行号
            // 
            this.行号.Caption = "行号";
            this.行号.FieldName = "行号";
            this.行号.Name = "行号";
            this.行号.Visible = true;
            this.行号.VisibleIndex = 2;
            // 
            // 供应商代码
            // 
            this.供应商代码.Caption = "供应商代码";
            this.供应商代码.FieldName = "供应商代码";
            this.供应商代码.Name = "供应商代码";
            this.供应商代码.Visible = true;
            this.供应商代码.VisibleIndex = 3;
            // 
            // 供应商名称
            // 
            this.供应商名称.Caption = "供应商名称";
            this.供应商名称.FieldName = "供应商名称";
            this.供应商名称.Name = "供应商名称";
            this.供应商名称.Visible = true;
            this.供应商名称.VisibleIndex = 4;
            // 
            // 料号
            // 
            this.料号.Caption = "料号";
            this.料号.FieldName = "料号";
            this.料号.Name = "料号";
            this.料号.Visible = true;
            this.料号.VisibleIndex = 5;
            // 
            // 料号名称
            // 
            this.料号名称.Caption = "料号名称";
            this.料号名称.FieldName = "料号名称";
            this.料号名称.Name = "料号名称";
            this.料号名称.Visible = true;
            this.料号名称.VisibleIndex = 6;
            // 
            // 订单数量
            // 
            this.订单数量.Caption = "订单数量";
            this.订单数量.FieldName = "订单数量";
            this.订单数量.Name = "订单数量";
            this.订单数量.Visible = true;
            this.订单数量.VisibleIndex = 7;
            // 
            // 剩余数量
            // 
            this.剩余数量.Caption = "剩余数量";
            this.剩余数量.FieldName = "剩余数量";
            this.剩余数量.Name = "剩余数量";
            this.剩余数量.Visible = true;
            this.剩余数量.VisibleIndex = 8;
            // 
            // 单位
            // 
            this.单位.Caption = "单位";
            this.单位.FieldName = "单位";
            this.单位.Name = "单位";
            this.单位.Visible = true;
            this.单位.VisibleIndex = 9;
            // 
            // 状态
            // 
            this.状态.Caption = "状态";
            this.状态.FieldName = "状态";
            this.状态.Name = "状态";
            this.状态.Visible = true;
            this.状态.VisibleIndex = 10;
            // 
            // 提货时间
            // 
            this.提货时间.Caption = "提货时间";
            this.提货时间.FieldName = "提货时间";
            this.提货时间.Name = "提货时间";
            this.提货时间.Visible = true;
            this.提货时间.VisibleIndex = 11;
            // 
            // 预计提货时间
            // 
            this.预计提货时间.Caption = "预计提货时间";
            this.预计提货时间.FieldName = "预计提货时间";
            this.预计提货时间.Name = "预计提货时间";
            this.预计提货时间.Visible = true;
            this.预计提货时间.VisibleIndex = 12;
            // 
            // 运输时间
            // 
            this.运输时间.Caption = "运输时间";
            this.运输时间.FieldName = "运输时间";
            this.运输时间.Name = "运输时间";
            this.运输时间.Visible = true;
            this.运输时间.VisibleIndex = 13;
            // 
            // bar1
            // 
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.Text = "Tools";
            // 
            // bar2
            // 
            this.bar2.BarName = "Tools";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar2.OptionsBar.AllowQuickCustomization = false;
            this.bar2.Text = "Tools";
            // 
            // bar3
            // 
            this.bar3.BarName = "Tools";
            this.bar3.DockCol = 0;
            this.bar3.DockRow = 0;
            this.bar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar3.OptionsBar.AllowQuickCustomization = false;
            this.bar3.Text = "Tools";
            // 
            // barMenu
            // 
            this.barMenu.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar4});
            this.barMenu.DockControls.Add(this.barDockControlTop);
            this.barMenu.DockControls.Add(this.barDockControlBottom);
            this.barMenu.DockControls.Add(this.barDockControlLeft);
            this.barMenu.DockControls.Add(this.barDockControlRight);
            this.barMenu.Form = this;
            this.barMenu.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.btnRefresh,
            this.btnExit,
            this.btnImportExcel,
            this.btnSave});
            this.barMenu.MaxItemId = 10;
            // 
            // bar4
            // 
            this.bar4.BarName = "Tools";
            this.bar4.DockCol = 0;
            this.bar4.DockRow = 0;
            this.bar4.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar4.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btnImportExcel),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.btnExit, true)});
            this.bar4.OptionsBar.AllowQuickCustomization = false;
            this.bar4.Text = "Tools";
            // 
            // btnImportExcel
            // 
            this.btnImportExcel.Caption = "导入";
            this.btnImportExcel.Id = 8;
            this.btnImportExcel.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnImportExcel.ImageOptions.Image")));
            this.btnImportExcel.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnImportExcel.ImageOptions.LargeImage")));
            this.btnImportExcel.Name = "btnImportExcel";
            this.btnImportExcel.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnImportExcel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnImportExcel_ItemClick);
            // 
            // btnSave
            // 
            this.btnSave.Caption = "保存";
            this.btnSave.Id = 9;
            this.btnSave.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnSave.ImageOptions.Image")));
            this.btnSave.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnSave.ImageOptions.LargeImage")));
            this.btnSave.Name = "btnSave";
            this.btnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnSave_ItemClick);
            // 
            // btnExit
            // 
            this.btnExit.Caption = "返回";
            this.btnExit.Id = 1;
            this.btnExit.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnExit.ImageOptions.SvgImage")));
            this.btnExit.Name = "btnExit";
            this.btnExit.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.btnExit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnReturn_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            this.barDockControlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.barDockControlTop.Location = new System.Drawing.Point(0, 0);
            this.barDockControlTop.Manager = this.barMenu;
            this.barDockControlTop.Size = new System.Drawing.Size(1176, 24);
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            this.barDockControlBottom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.barDockControlBottom.Location = new System.Drawing.Point(0, 578);
            this.barDockControlBottom.Manager = this.barMenu;
            this.barDockControlBottom.Size = new System.Drawing.Size(1176, 0);
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            this.barDockControlLeft.Dock = System.Windows.Forms.DockStyle.Left;
            this.barDockControlLeft.Location = new System.Drawing.Point(0, 24);
            this.barDockControlLeft.Manager = this.barMenu;
            this.barDockControlLeft.Size = new System.Drawing.Size(0, 554);
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            this.barDockControlRight.Dock = System.Windows.Forms.DockStyle.Right;
            this.barDockControlRight.Location = new System.Drawing.Point(1176, 24);
            this.barDockControlRight.Manager = this.barMenu;
            this.barDockControlRight.Size = new System.Drawing.Size(0, 554);
            // 
            // btnRefresh
            // 
            this.btnRefresh.Caption = "刷新";
            this.btnRefresh.Id = 0;
            this.btnRefresh.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnRefresh.ImageOptions.SvgImage")));
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // 错误信息
            // 
            this.错误信息.Caption = "错误信息";
            this.错误信息.FieldName = "错误信息";
            this.错误信息.Name = "错误信息";
            this.错误信息.Visible = true;
            this.错误信息.VisibleIndex = 14;
            // 
            // frmImportSTWK
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 14F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1176, 578);
            this.Controls.Add(this.groupControlImported);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frmImportSTWK";
            this.Text = "数据导入管理";
            this.Load += new System.EventHandler(this.frmImportSTWK_Load);
            ((System.ComponentModel.ISupportInitialize)(this.bs)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlImported)).EndInit();
            this.groupControlImported.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gd)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gdv)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barMenu)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private DevExpress.XtraEditors.GroupControl groupControlImported;
        private DevExpress.XtraGrid.GridControl gd;
        private DevExpress.XtraGrid.Views.Grid.GridView gdv;
        private DevExpress.XtraGrid.Columns.GridColumn 处理人;
        private DevExpress.XtraGrid.Columns.GridColumn 订单号;
        private DevExpress.XtraGrid.Columns.GridColumn 行号;
        private DevExpress.XtraGrid.Columns.GridColumn 供应商代码;
        private DevExpress.XtraGrid.Columns.GridColumn 供应商名称;
        private DevExpress.XtraGrid.Columns.GridColumn 料号;
        private DevExpress.XtraGrid.Columns.GridColumn 料号名称;
        private DevExpress.XtraGrid.Columns.GridColumn 订单数量;
        private DevExpress.XtraGrid.Columns.GridColumn 剩余数量;
        private DevExpress.XtraGrid.Columns.GridColumn 单位;
        private DevExpress.XtraGrid.Columns.GridColumn 状态;
        private DevExpress.XtraGrid.Columns.GridColumn 提货时间;
        private DevExpress.XtraGrid.Columns.GridColumn 预计提货时间;
        private DevExpress.XtraGrid.Columns.GridColumn 运输时间;
        public DevExpress.XtraBars.Bar bar1;
        public DevExpress.XtraBars.Bar bar2;
        public DevExpress.XtraBars.Bar bar3;
        public DevExpress.XtraBars.BarManager barMenu;
        public DevExpress.XtraBars.Bar bar4;
        public DevExpress.XtraBars.BarButtonItem btnRefresh;
        private DevExpress.XtraBars.BarButtonItem btnImportExcel;
        private DevExpress.XtraBars.BarButtonItem btnSave;
        public DevExpress.XtraBars.BarButtonItem btnExit;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraGrid.Columns.GridColumn 错误信息;
    }
}